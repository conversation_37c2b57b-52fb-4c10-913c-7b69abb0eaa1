import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()
from django.utils import timezone
from helpers.decode_id import decode_token
from Authentication.models import User
from Project.models import *
import base64
import datetime
from flask import Flask, request
from flask_socketio import SocketIO, emit, join_room
from flask_cors import CORS
from flask import request
from helpers.base_64_file import save_base64_file
from helpers.send_noti import send_in_app_notification


app = Flask(__name__)
app.config['SECRET_KEY'] = 'your_secret_key'
app.config['UPLOAD_FOLDER'] = os.path.join('media', 'prepare_chat_files')

CORS(app, resources={r"/*": {"origins": "*"}})  # Allow firecamp.dev
socketio = SocketIO(app, cors_allowed_origins=["https://firecamp.dev","https://staging.flowkar.com","http://staging.flowkar.com","https://api.flowkar.com","http://localhost:3000","http://dev.flowkar.com","https://dev.flowkar.com","https://beta.flowkar.com","https://app.flowkar.com"], max_http_buffer_size=1024*1024*400, ping_timeout=120, ping_interval=25)



@socketio.on('join_socket')
def join_socket(data):
    try:
        auth_token = data.get('Authorization')
        if not auth_token:
            emit('join_socket', {'message': 'JWT Token Required'})
            return
        user_id = decode_token(f'Bearer {auth_token}')
        if not user_id:
            emit('join_socket', {'message': 'Invalid JWT Token'})
            return
        user_socket_id = request.sid
        user_data = User.objects.get(pk=user_id)
        user_data.socket_id = user_socket_id
        user_data.save()
        room = str(user_id)
        if not user_socket_id:
            emit('join_socket', {'message': 'User not connected'})
            return

        # Join the user to the room
        join_room(room)

        # Emit a success message to the user
        emit('join_socket', {'message': f'Joined room {room}'}, room=room)
        print(user_socket_id)
    except Exception as e:
        emit("join_socket", {'status': False, 'error': str(e)}), 500
        return


@socketio.on('send_message')
def send_message(data):
    try:
        auth_token = data.get('Authorization')
        message = data.get('message', '')
        type = data.get('type', 'text')
        message_id = data.get('message_id', '')
        file = data.get('file', '')
        to_user_id = data.get('to')
        if not auth_token:
            emit('send_message', {'message': 'JWT Token Required'})
            return
        user_id = decode_token(f'Bearer {auth_token}')
        if not user_id:
            emit('send_message', {'message': 'Invalid JWT Token'})
            return
        room = str(to_user_id)
        print(f'room --> {room}')
        if type == 'reply_message':
            if message_id is None or message_id == '':
                emit('send_message', {
                     'message': 'To reply to a message Id is required'})
                return
            else:
                create = ChatMessage.objects.create(
                    from_user_id=user_id,
                    to_user_id=to_user_id,
                    type=type,
                    message_id=message_id,
                    messages=message
                )

                noti_data = {
                        'user_id': create.to_user.pk,
                        'type': 'message',
                        'title': 'Flowkar',
                        'message': f'{create.from_user.name} Messaged You',
                        'messages': message,
                        "from":create.from_user.pk
                    }
                socketio.emit('receive_message', {'id': create.pk, 'message': message, 'type': type,
                              'created_at': f'{timezone.localtime(create.created_at).isoformat()}', 'sent_by': user_id}, to=room)
                emit('send_message', {'id': create.pk, 'message': message, 'type': type,
                     'created_at': f'{timezone.localtime(create.created_at).isoformat()}', 'sent_by': user_id})
                send_in_app_notification(
                    noti_data, User, socketio.emit)
                return
        if type in ['image', 'voice', 'custom']:
            if file is None or file == '':
                emit('send_message', {
                     'message': 'For type file or voice a file is required'})
                return
            else:
                file_path = None
                file_path = save_base64_file(
                    file, user_id, datetime.datetime.now())
                create = ChatMessage.objects.create(
                    from_user_id=user_id,
                    to_user_id=to_user_id,
                    type=type,
                    file=file_path,
                    message_id=message_id,
                    messages=f'Sent you an {type}'
                )
                noti_data = {
                        'user_id': create.to_user.pk,
                        'type': 'message',
                        'title': 'Flowkar',
                        'message': f'{create.from_user.name} Sent You A File',
                        'messages': message,
                        "from":create.from_user.pk
                    }
                socketio.emit('receive_message', {'id': create.pk, 'message': file_path, 'type': type,
                              'file': file_path, 'created_at': f'{timezone.localtime(create.created_at).isoformat()}', 'sent_by': user_id}, to=room)
                emit('send_message', {'id': create.pk, 'message': file_path, 'type': type,
                     'file': file_path, 'created_at': f'{timezone.localtime(create.created_at).isoformat()}', 'sent_by': user_id})
                send_in_app_notification(
                    noti_data, User, socketio.emit)
                return
        create = ChatMessage.objects.create(
            from_user_id=user_id,
            to_user_id=to_user_id,
            type=type,
            messages=message
        )
        noti_data = {
                        'user_id': create.to_user.pk,
                        'type': 'message',
                        'title': 'Flowkar',
                        'message': f'{create.from_user.name} Messaged You',
                        'messages': message,
                        "from":create.from_user.pk
                    }
        socketio.emit('receive_message', {'id': create.pk, 'message': message, 'type': type,
                      'created_at': f'{timezone.localtime(create.created_at).isoformat()}', 'sent_by': user_id}, to=room)

        emit('send_message', {'id': create.pk, 'message': message, 'type': type,
             'created_at': f'{timezone.localtime(create.created_at).isoformat()}', 'sent_by': user_id})
        send_in_app_notification(
                    noti_data, User, socketio.emit)
    except Exception as e:
        emit("send_message", {'status': False, 'error': str(e)}), 500
        return


@socketio.on('delete_message')
def delete_message(data):
    auth_token = data.get('Authorization')
    message_id = data.get('message_id')
    if not auth_token:
        emit('delete_message', {'message': 'JWT Token Required'})
        return
    user_id = decode_token(f'Bearer {auth_token}')
    if not user_id:
        emit('delete_message', {'message': 'Invalid JWT Token'})
        return
    try:
        message = ChatMessage.objects.get(pk=message_id)
        to_user_id = message.to_user_id
        message.delete()
        emit('delete_message', {
            'message': 'Message deleted successfully',
            'message_id': message_id,
            'to_user_id': to_user_id
        })
    except ChatMessage.DoesNotExist:
        emit('delete_message', {'message': 'Message not found'})


@socketio.on('is_typing')
def is_typing(data):
    auth_token = data.get('Authorization')
    to_room = data.get('to')
    is_typing = data.get('is_typing')
    if not auth_token:
        emit('is_typing', {'message': 'JWT Token Required'})
        return
    user_id = decode_token(f'Bearer {auth_token}')
    if not user_id:
        emit('is_typing', {'message': 'Invalid JWT Token'})
        return
    room = str(to_room)
    if is_typing == '1':
        socketio.emit('is_typing', {'is_typing': True , 'to':to_room ,'user_id':user_id}, to=room)
    else:
        socketio.emit('is_typing', {'is_typing': False}, to=room)


@socketio.on('message_read')
def is_typing(data):
    auth_token = data.get('Authorization')
    to_room = data.get('to')
    message_id = data.get('message_id')
    if not auth_token:
        emit('message_read', {'message': 'JWT Token Required'})
        return
    user_id = decode_token(f'Bearer {auth_token}')
    if not user_id:
        emit('message_read', {'message': 'Invalid JWT Token'})
        return
    if not message_id:
        emit('message_read', {'message': 'message_id field is required'})
        return

    room = str(to_room)

    try:
        message = ChatMessage.objects.get(pk=message_id)
        message.is_read = True
        message.read_time = datetime.datetime.now()
        message.save()
        socketio.emit('message_read', {
                      'id': message.pk, 'is_read': True, 'read_time': message.read_time}, to=room)

    except ChatMessage.DoesNotExist:
        emit('message_read', {'message': 'Chat message not found'})


if __name__ == '__main__':
    socketio.run(app, allow_unsafe_werkzeug=True, debug=True, port=20044)